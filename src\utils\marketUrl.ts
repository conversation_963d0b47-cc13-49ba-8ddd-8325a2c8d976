import { FRONTEND } from "@/constants/config";

/**
 * Map locale codes to language parameters for market URL
 */
const getLanguageParam = (locale: string): string => {
    switch (locale) {
        case "en-US":
            return "en";
        case "zh-HK":
            return "zh";
        case "th-TH":
            return "th";
        default:
            return "en"; // fallback to English
    }
};

/**
 * Generate market URL with user hash and language parameters
 * @param userIdHash - User's hashed ID (optional)
 * @param locale - Current locale (optional, defaults to "en-US")
 * @returns Complete market URL with parameters
 */
export const generateMarketUrl = (userIdHash?: string, locale?: string): string => {
    const baseUrl = FRONTEND.MARKET_URL;
    const languageParam = getLanguageParam(locale || "en-US");

    try {
        const url = new URL(baseUrl);

        // Add language parameter
        url.searchParams.set('locales', languageParam);

        // Add user parameter if available
        if (userIdHash) {
            url.searchParams.set('user', btoa(userIdHash));
        }

        return url.toString();
    } catch (error) {
        console.error("Error generating market URL:", error);
        return baseUrl;
    }
};

/**
 * Get the base market URL for the current environment
 * @returns Base market URL without parameters
 */
export const getMarketBaseUrl = (): string => {
    return FRONTEND.MARKET_URL;
};
