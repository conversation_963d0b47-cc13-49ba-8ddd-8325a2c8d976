{"name": "nextjs-<PERSON>user<PERSON>b", "version": "0.2.547", "packageManager": "yarn@4.9.2", "private": true, "scripts": {"dev": "cross-env PROJECT_FRONTEND_ENV=LOC PROJECT_BACKEND_ENV=DEV next dev", "loc": "cross-env PROJECT_FRONTEND_ENV=LOC PROJECT_BACKEND_ENV=LOC next dev", "build": "next build", "build:sitemap": "next build && next-sitemap", "start": "next start -p 3000", "lint": "next lint", "watch": "tsc --watch --noEmit"}, "dependencies": {"@easylive-show/react-phone-input": "^2.14.10", "@easylive-show/video-player": "^1.3.11", "@easylive-show/videojs-hls-quality-selector": "1.1.5", "@easylive-show/videojs-plugin-contextmenu": "^1.0.3", "@emotion/cache": "^11.4.0", "@emotion/react": "^11.11.1", "@emotion/server": "^11.4.0", "@emotion/styled": "^11.11.0", "@fingerprintjs/fingerprintjs": "^3.3.0", "@mui/base": "^5.0.0-beta.18", "@mui/icons-material": "5.x", "@mui/lab": "^6.0.0-beta.15", "@mui/material": "5.x", "@mui/material-nextjs": "^6.1.6", "@mui/styles": "5.x", "@mui/system": "5.x", "@mui/x-date-pickers": "^6.9.2", "@reduxjs/toolkit": "^1.9.2", "@stoneleigh/api-lib": "6.1.1", "@stoneleigh/appconfig-lib": "^1.2.0", "@stoneleigh/navigation-menu": "^1.4.4", "@stoneleigh/scroll-header": "^0.0.24", "axios": "^0.24.0", "babel-plugin-macros": "^3.1.0", "base-64": "^1.0.0", "classnames": "^2.3.1", "cookie": "^0.5.0", "cookies-next": "^2.1.2", "countries-list": "^3.1.1", "date-fns": "^2.30.0", "dayjs": "^1.11.9", "dayjs-plugin-utc": "^0.1.2", "dotenv": "^10.0.0", "fg-loadcss": "^3.1.0", "framer-motion": "^10.16.4", "i18next": "23.2.11", "js-sha256": "^0.9.0", "lodash": "^4.17.21", "lodash.throttle": "^4.1.1", "markdown-to-jsx": "^7.3.2", "moment": "^2.29.1", "moment-timezone": "^0.5.34", "mui-file-input": "^6.0.0", "next": "13.4.12", "next-i18next": "13.3.0", "notistack": "^3.0.1", "qrcode.react": "^1.0.1", "react": "18.x", "react-day-picker": "^9.1.3", "react-dom": "18.x", "react-i18next": "13.0.2", "react-image-gallery": "^1.3.0", "react-multi-carousel": "^2.8.4", "react-player": "^2.13.0", "react-qr-code": "^2.0.15", "react-redux": "^8.0.5", "react-share": "^4.4.0", "react-sticky-el": "^2.1.0", "react-use": "^17.4.0", "redux": "^4.2.1", "redux-cookie": "^0.5.9", "redux-logger": "^3.0.6", "redux-persist": "^6.0.0", "rfs": "10.x", "rxjs": "^7.8.1", "sass": "^1.43.2", "styled-components": "^6.0.4", "universal-cookie": "^4.0.4", "usehooks-ts": "^2.9.1", "uuid": "^9.0.1"}, "devDependencies": {"@next/swc-linux-arm64-musl": "13.4.12", "@next/swc-linux-x64-musl": "13.4.12", "@stoneleigh/eslint-plugin": "^1.0.16", "@types/babel-plugin-macros": "^3", "@types/cookie": "^0", "@types/eslint": "^8", "@types/fg-loadcss": "^3.1.1", "@types/lodash": "^4", "@types/lodash.throttle": "^4", "@types/node": "^18.6.2", "@types/react": "18.x", "@types/react-dom": "18.x", "@types/react-image-gallery": "^1", "@types/react-linkify": "^1.0.1", "@types/react-redux": "^7.1.20", "@types/react-router-dom": "^5.3.2", "@types/redux-logger": "^3.0.9", "@types/uuid": "^9", "@typescript-eslint/eslint-plugin": "^6.7.3", "@typescript-eslint/parser": "^6.7.3", "cross-env": "^7.0.3", "eslint": "^8.35.0", "eslint-config-next": "^13.2.4", "eslint-plugin-eslint-plugin": "^5.1.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-node": "^11.1.0", "next-sitemap": "^4.1.8", "typescript": "5.x"}, "peerDependencies": {"cross-env": "^7.0.3"}, "browserslist": ["defaults", "last 2 versions", "not dead", ">0.2%"], "resolutions": {"@types/react": "18.x"}}