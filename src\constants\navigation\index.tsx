import UserAccountMenu from "@/components/Layouts/Header/UserAccountMenu";
import { ReactNode } from "react";

type Badge = {
  label: string;
  className?: string;
  color?: string;
  backgroundColor?: string;
};
export interface INavigationItem {
  type: "item";
  hidden?: boolean;
  translationKey?: string;
  path?: string;
  onClick?(): void;
  newTab?: boolean;
  badge?: Badge;
  icon?: string;
  directionicon?: "horizontal" | "vertical";
  direction?: "horizontal" | "vertical";
  beforeLine?: boolean;
  close?: boolean;
  content?: ReactNode;
}

export interface INavigationExternalItem {
  type: "external";
  hidden?: boolean;
  translationKey?: string;
  path?: string;
  onClick?(): void;
  newTab?: boolean;
  badge?: Badge;
  icon?: string;
  directionicon?: "horizontal" | "vertical";
  direction?: "horizontal" | "vertical";
  beforeLine?: boolean;
  close?: boolean;
  content?: ReactNode;
}
export interface INavigationGroup {
  type: "group";
  hidden?: boolean;
  badge?: Badge;
  translationKey: string;
  items: INavigationItem[];
}

export interface INavigationSubmenu {
  type: "submenu";
  hidden?: boolean;
  variant?: "normal" | "mega";
  defaultExpanded?: boolean;
  badge?: Badge;
  translationKey: string;
  items: Array<
    | INavigationItem
    | INavigationGroup
    | INavigationSubmenu
    | INavigationMegaColumn
  >;
}

export interface INavigationMegaColumn {
  type: "megaColumn";
  hidden?: boolean;
  items: Array<INavigationItem | INavigationGroup>;
}

export interface INavigationListParams {
  currentPath?: string;
  isLoggedIn?: boolean;
  orientation?: "vertical" | "horizontal";
  logoutAsync?(): void;
  locale?: string;
}

export type INavigationList = Array<INavigationItem | INavigationExternalItem | INavigationGroup | INavigationSubmenu | INavigationMegaColumn>
export const NavigationList: INavigationList = [
    {
      type: "item",
      translationKey: "about",
      path: "/about",
    },
    {
      type: "external",
      translationKey: "market",
      path: "https://dev-market.incutix.com"
    },
    {
      type: "item",
      translationKey: "guide",
      path: "/guide"
    },
    {
      type: "item",
      translationKey: "faq",
      path: "/faq",
    },
    {
      type: "item",
      content: <UserAccountMenu />,
    },
];