import { APIConfig, GenericAPI } from "@stoneleigh/api-lib";
import AppConfig, { EnumConfigEnv } from "@stoneleigh/appconfig-lib";

import OSEnvironment from "./OSEnvironment";

AppConfig.Init({
  defaultEnv: process.env["PROJECT_BACKEND_ENV"],
});

AppConfig.set({
  env: EnumConfigEnv.LOC,
  frontend: {
    url: "http://localhost:3000",
    accessKey:
      "****************************************************************",
    shouldCheckAccessKey: false,
    googleAnalyticsKey: "",
    facebookPixelId: "***************",
    NameMinLength: 1,
    AccountMinLength: 5,
    PasswordMinLength: 8,
    VerifyPhoneNumberRetrySecond: 90,
    MaxPurchaseTicketNumber: 10,
    RecaptchaV3:""
  },
  backend: {
    assetsEndpoint: "https://assets.incutix.com",
    api: "http://localhost:65481",
    // api: "https://790b-202-155-203-230.ngrok-free.app/api"
    appSecret: "qECcmDqknMaTdVtRG+4UbRN6zQ7YVuDr7pfSVG/HgsZQ5ZNDeXeQRFLfDeF+c/3F",
  },
});

AppConfig.set({
  env: EnumConfigEnv.DEV,
  frontend: {
    url: "https://dev.incutix.com",
    accessKey:
      "****************************************************************",
    shouldCheckAccessKey: false,
    googleAnalyticsKey: "",
    facebookPixelId: "***************",
    NameMinLength: 1,
    AccountMinLength: 5,
    PasswordMinLength: 8,
    VerifyPhoneNumberRetrySecond: 90,
    MaxPurchaseTicketNumber: 10,
    RecaptchaV3:"6LeGT_QcAAAAAKafTnLUuM6JXQi57ELSHI97xYgr"
  },
  backend: {
    assetsEndpoint: "https://assets.incutix.com",
    api: "https://dev-api.incutix.com",
    appSecret: "qECcmDqknMaTdVtRG+4UbRN6zQ7YVuDr7pfSVG/HgsZQ5ZNDeXeQRFLfDeF+c/3F",
  },
});

AppConfig.set({
  env: EnumConfigEnv.UAT,
  frontend: {
    url: "https://uat.incutix.com",
    accessKey:
      "****************************************************************",
    shouldCheckAccessKey: true,
    googleAnalyticsKey: "",
    facebookPixelId: "***************",
    NameMinLength: 1,
    AccountMinLength: 5,
    PasswordMinLength: 8,
    VerifyPhoneNumberRetrySecond: 90,
    MaxPurchaseTicketNumber: 10,
    RecaptchaV3:"6LcbfvQcAAAAAD_gStZI43G6oJGo-oV4k1Z65g7f"
  },
  backend: {
    url: "#",
    assetsEndpoint: "https://assets.incutix.com",
    strapi: "#",
    api: "https://sit-api.incutix.com",
    appSecret: "qECcmDqknMaTdVtRG+4UbRN6zQ7YVuDr7pfSVG/HgsZQ5ZNDeXeQRFLfDeF+c/3F",
  },
});

AppConfig.set({
  env: EnumConfigEnv.PREPRD,
  frontend: {
    url: "https://preprd.incutix.com",
    accessKey:
      "****************************************************************",
    shouldCheckAccessKey: true,
    googleAnalyticsKey: "",
    facebookPixelId: "***************",
    NameMinLength: 1,
    AccountMinLength: 5,
    PasswordMinLength: 8,
    VerifyPhoneNumberRetrySecond: 90,
    MaxPurchaseTicketNumber: 10,
    RecaptchaV3:"6LeQXPQcAAAAAEFuX0YVGCXtD7JJ6oWJP6w9xc3G"
  },
  backend: {
    assetsEndpoint: "https://assets.incutix.com",
    api: "https://preprd-api.incutix.com",
    appSecret: "qECcmDqknMaTdVtRG+4UbRN6zQ7YVuDr7pfSVG/HgsZQ5ZNDeXeQRFLfDeF+c/3F",
  },
});

AppConfig.set({
  env: EnumConfigEnv.PRD,
  frontend: {
    url: "https://incutix.com",
    accessKey:
      "****************************************************************",
    shouldCheckAccessKey: false,
    googleAnalyticsKey: "G-T627RJCD56",
    facebookPixelId: "***************",
    NameMinLength: 1,
    AccountMinLength: 5,
    PasswordMinLength: 8,
    VerifyPhoneNumberRetrySecond: 90,
    MaxPurchaseTicketNumber: 10,
    RecaptchaV3:"6LeQXPQcAAAAAEFuX0YVGCXtD7JJ6oWJP6w9xc3G"
  },
  backend: {
    assetsEndpoint: "https://assets.incutix.com",
    api: "https://api.incutix.com",
    appSecret: "qECcmDqknMaTdVtRG+4UbRN6zQ7YVuDr7pfSVG/HgsZQ5ZNDeXeQRFLfDeF+c/3F",
  },
});

class FRONTEND {
  static readonly ENV = OSEnvironment.FRONTEND_ENV;
  static readonly IS_PRODUCTION = this.ENV === EnumConfigEnv.PRD;
  static readonly URL = AppConfig.get({
    key: "frontend.url",
    env: OSEnvironment.FRONTEND_ENV,
  });
  static readonly ACCESS_KEY = AppConfig.get({
    key: "frontend.accessKey",
    env: OSEnvironment.FRONTEND_ENV,
  });
  static readonly SHOULD_CHECK_ACCESS_KEY = AppConfig.get<boolean>({
    key: "frontend.shouldCheckAccessKey",
    env: OSEnvironment.FRONTEND_ENV,
  });
  static readonly GOOGLE_ANALYTICS_KEY = AppConfig.get({
    key: "frontend.googleAnalyticsKey",
    env: OSEnvironment.FRONTEND_ENV,
  });
  static readonly FACEBOOK_PIXEL_ID = AppConfig.get({
    key: "frontend.facebookPixelId",
    env: OSEnvironment.FRONTEND_ENV,
  });
  static readonly NameMinLength = parseInt(AppConfig.get({
    key: "frontend.NameMinLength",
    env: OSEnvironment.FRONTEND_ENV,
  }) as string);
  static readonly AccountMinLength = parseInt(AppConfig.get({
    key: "frontend.AccountMinLength",
    env: OSEnvironment.FRONTEND_ENV,
  }) as string);
  static readonly PasswordMinLength = parseInt(AppConfig.get({
    key: "frontend.PasswordMinLength",
    env: OSEnvironment.FRONTEND_ENV,
  }) as string);
  static readonly VerifyPhoneNumberRetrySecond = parseInt(AppConfig.get({
    key: "frontend.VerifyPhoneNumberRetrySecond",
    env: OSEnvironment.FRONTEND_ENV,
  }) as string);
  static readonly MaxPurchaseTicketNumber = parseInt(AppConfig.get({
    key: "frontend.MaxPurchaseTicketNumber",
    env: OSEnvironment.FRONTEND_ENV,
  }) as string);
  static readonly RecaptchaV3 = AppConfig.get({
    key: "frontend.RecaptchaV3",
    env: OSEnvironment.FRONTEND_ENV,
  });
}

class BACKEND {
  static readonly ASSETS_ENDPOINT = AppConfig.get({
    key: "backend.assetsEndpoint",
    env: OSEnvironment.BACKEND_ENV,
  });
  static readonly INTERNAL_API_ENDPOINT = AppConfig.get({
    key: "backend.api",
    env: OSEnvironment.BACKEND_ENV,
  });
  static readonly APP_SECRET = AppConfig.get({
    key: "backend.appSecret",
    env: OSEnvironment.BACKEND_ENV,
  });
  static get Gateway() {
    return APIConfig.Gateways["internal"] as GenericAPI;
  }
}

export { FRONTEND, BACKEND };
