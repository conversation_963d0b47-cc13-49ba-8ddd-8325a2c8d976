import NavigationExternalItem from "./ExternalItem";
import NavigationMenuItem from "./Item";
import styles from "./menu.module.scss";
import { EnumTranslationJson } from "@/constants/enum/TranslationJsonKey";
import { INavigationList, NavigationList } from "@/constants/navigation";
import { dispatch, useSelector } from "@/redux/store";
import Menu, { ItemGroup, MegaColumn, SubMenu } from "@stoneleigh/navigation-menu";
import classNames from "classnames";
import { useTranslation } from "next-i18next";
import { useRouter } from "next/router";
import React, { useCallback, useEffect, useMemo, useState } from "react";

interface NavigationMenuProps {
    direction: "horizontal" | "vertical";
}

const NavigationMenu = (props: NavigationMenuProps) => {
    const { direction = "horizontal" } = props;
    const userIdHash = useSelector(state => state.user.userIdHash);
    const router = useRouter();
    const [isHydrated, setIsHydrated] = useState(false);

    // Map locale codes to language parameters for merchandise URL
    const getLanguageParam = (locale: string) => {
        switch (locale) {
            case "en-US":
                return "en";
            case "zh-HK":
                return "zh";
            case "th-TH":
                return "th";
            default:
                return "en"; // fallback to English
        }
    };

    // Handle client-side hydration
    useEffect(() => {
        setIsHydrated(true);
    }, [userIdHash]);

    // Generate market URL with user hash and language parameters
    const marketUrl = useMemo(() => {
        const baseUrl = 'https://dev-market.incutix.com';
        const languageParam = getLanguageParam(router.locale || "en-US");

        try {
            const url = new URL(baseUrl);

            // Add language parameter
            url.searchParams.set('locales', languageParam);

            // Add user parameter if available
            if (userIdHash) {
                url.searchParams.set('user', btoa(userIdHash));
            }

            return url.toString();
        } catch (error) {
            console.error("Error generating market URL:", error);
            return baseUrl;
        }
    }, [userIdHash, router.locale, getLanguageParam]);
    const { t: navigationTranslation } = useTranslation(EnumTranslationJson.Navigation);
    const createMenuItems = useCallback((items: INavigationList) => {
        return items
            .filter((x) => !x.hidden)
            .map((item, i) => {
                switch (item.type) {
                    case "megaColumn":
                        return (
                            <MegaColumn className={direction == "horizontal"} key={i}>
                                {createMenuItems(item.items)}
                            </MegaColumn>
                        );
                    case "submenu":
                        return (
                            <SubMenu
                                key={i}
                                label={navigationTranslation(`main.${item.translationKey}`)}
                                badge={item.badge}
                                variant={item.variant}
                                defaultExpanded={item.defaultExpanded}
                                className={item.variant}
                            >
                                {createMenuItems(item.items)}
                            </SubMenu>
                        );
                    case "group":
                        return (
                            <ItemGroup
                                key={i}
                                label={navigationTranslation(`main.${item.translationKey}`)}
                                badge={item.badge}
                            >
                                {createMenuItems(item.items)}
                            </ItemGroup>
                        );
                    case "item":
                        return <NavigationMenuItem key={i} {...item} directionicon={direction}/>;
                    case "external": {
                        // Use a key that includes userIdHash and locale to force re-render when user token or language changes
                        const externalKey = `external-${i}-${userIdHash ?? 'no-user'}-${router.locale ?? 'no-locale'}-${isHydrated ? 'hydrated' : 'not-hydrated'}`;
                        return <NavigationExternalItem key={externalKey} {...item} directionicon={direction} path={marketUrl} />;
                    }
                    default:
                        return <></>;
                }
            });
    }, [direction, navigationTranslation, marketUrl, userIdHash, isHydrated]);

    const MenuItems = useMemo(() => createMenuItems(NavigationList), [createMenuItems]);
    return (
        <Menu
            direction={direction}
            colorTheme={{
                background: "inherit",
                submenuBackground: direction == "horizontal" && "#000000",
                textHover: "inherit",
            }}
            className={classNames(styles[direction], styles.menu)}
        >
            {MenuItems}
        </Menu>
    );
};
export default NavigationMenu;
